import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Chip,
  FormHelperText,
  Autocomplete
} from '@mui/material';
import { toaster } from '../../../Components/Toaster';
import { apiCallV2, apiCall, apiCallSmart } from '../../../Utilities';

interface CreateModelDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// Feature definitions with threshold types (V2 API)
const FEATURES = [
  { id: 'fraud', name: 'Fraud', type: 'correlation' },
  { id: 'synthetic', name: 'Synthetic', type: 'synthetic' },
  { id: 'correlation', name: 'Correlation', type: 'correlation' },
  { id: 'risk', name: 'Risk Score', type: 'risk' }
];

// Products for V2 API
const PRODUCTS = [
  { id: 1, name: 'Sigma Identity' },
  { id: 2, name: 'Sigma Synthetic' },
  { id: 3, name: 'Address Risk' },
  { id: 4, name: 'Email Risk' },
  { id: 5, name: 'Phone Risk' }
];

// Engine is fixed to H2O for all models
const ENGINE = 'h2o';

export const CreateModelDialog: React.FC<CreateModelDialogProps> = ({
  open,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState({
    name: '',
    commonName: '',
    description: '',
    productId: '',
    defaultResponseName: '',
    defaultResponseVersion: '',
    feature: '',
    engine: ENGINE,
    h2oIdentifier: '',
    isDefault: false,
    isLegacy: false,
    isImplicit: false,
    // Threshold fields for config
    lowerThreshold: '',
    upperThreshold: '',
    positiveThreshold: '',
    negativeThreshold: '',
    mutedParams: ''
  });

  const [availableIdentifiers, setAvailableIdentifiers] = useState<string[]>([]);
  const [selectedFeature, setSelectedFeature] = useState<any>(null);
  const [loadingIdentifiers, setLoadingIdentifiers] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  // Load H2O POJO identifiers (POJO models are the current standard)
  const loadIdentifiers = async () => {
    setLoadingIdentifiers(true);
    try {
      // Use smart API call that routes v1 APIs correctly to get POJO models only
      const response = await apiCallSmart('/api/1/fraudmodel/list_mojo', {
        method: 'GET'
      });

      if (response.data && Array.isArray(response.data)) {
        setAvailableIdentifiers(response.data.sort());
      } else {
        setAvailableIdentifiers([]);
      }
    } catch (error) {
      console.error('Error loading POJO identifiers:', error);
      setAvailableIdentifiers([]);
      toaster.error('Failed to load H2O POJO identifiers');
    } finally {
      setLoadingIdentifiers(false);
    }
  };

  // Handle feature selection change
  const handleFeatureChange = (featureId: string) => {
    const feature = FEATURES.find(f => f.id.toString() === featureId);
    setSelectedFeature(feature);
    handleInputChange('feature', featureId);

    // Reset threshold values when feature changes
    handleInputChange('lowerThreshold', '');
    handleInputChange('upperThreshold', '');
    handleInputChange('positiveThreshold', '');
    handleInputChange('negativeThreshold', '');
    handleInputChange('mutedParams', '');

    // Reset flags when feature changes
    handleInputChange('isDefault', false);
    handleInputChange('isImplicit', false);
  };

  // Helper functions to determine threshold visibility
  const isRiskScore = () => selectedFeature?.type === 'risk';
  const isCorrelationScore = () => selectedFeature?.type === 'correlation';
  const isSyntheticScore = () => selectedFeature?.type === 'synthetic';

  // Load identifiers when component mounts
  useEffect(() => {
    if (open) {
      loadIdentifiers();
    }
  }, [open]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Model name is required';
    }

    if (!formData.commonName.trim()) {
      newErrors.commonName = 'Common name is required';
    }

    if (!formData.productId) {
      newErrors.productId = 'Product is required';
    }

    if (!formData.defaultResponseName.trim()) {
      newErrors.defaultResponseName = 'Default response name is required';
    }

    if (!formData.defaultResponseVersion.trim()) {
      newErrors.defaultResponseVersion = 'Default response version is required';
    }

    if (!formData.h2oIdentifier.trim()) {
      newErrors.h2oIdentifier = 'H2O Identifier is required';
    } else if (!availableIdentifiers.includes(formData.h2oIdentifier)) {
      newErrors.h2oIdentifier = 'Please select a valid H2O identifier from the list';
    }

    if (!formData.feature) {
      newErrors.feature = 'Feature is required';
    }

    // Validate thresholds based on feature type
    if (isRiskScore()) {
      if (formData.lowerThreshold && isNaN(parseFloat(formData.lowerThreshold))) {
        newErrors.lowerThreshold = 'Lower threshold must be a valid number';
      }
      if (formData.upperThreshold && (isNaN(parseFloat(formData.upperThreshold)) || parseFloat(formData.upperThreshold) < 0 || parseFloat(formData.upperThreshold) > 1)) {
        newErrors.upperThreshold = 'Upper threshold must be a number between 0.0 and 1.0';
      }
    }

    if (isCorrelationScore() || isSyntheticScore()) {
      if (formData.positiveThreshold && isNaN(parseFloat(formData.positiveThreshold))) {
        newErrors.positiveThreshold = 'Positive threshold must be a valid number';
      }
      if (formData.negativeThreshold && (isNaN(parseFloat(formData.negativeThreshold)) || parseFloat(formData.negativeThreshold) < 0 || parseFloat(formData.negativeThreshold) > 1)) {
        newErrors.negativeThreshold = 'Negative threshold must be a number between 0.0 and 1.0';
      }
    }



    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    try {
      // Generate configuration JSON string based on feature type
      const configObj: any = {};

      if (isRiskScore()) {
        if (formData.lowerThreshold) {
          configObj.low_risk_threshold = parseFloat(formData.lowerThreshold);
        }
        if (formData.upperThreshold) {
          configObj.high_risk_threshold = parseFloat(formData.upperThreshold);
        }
      }

      if (isCorrelationScore() || isSyntheticScore()) {
        if (formData.positiveThreshold) {
          configObj.positive_threshold = parseFloat(formData.positiveThreshold);
        }
        if (formData.negativeThreshold) {
          configObj.negative_threshold = parseFloat(formData.negativeThreshold);
        }
      }

      if (formData.mutedParams) {
        configObj.muted_params = formData.mutedParams;
      }

      const payload: any = {
        name: formData.name,
        commonName: formData.commonName,
        productId: parseInt(formData.productId as string),
        defaultResponseName: formData.defaultResponseName,
        defaultResponseVersion: parseFloat(formData.defaultResponseVersion),
        feature: formData.feature,
        engine: formData.engine,
        description: formData.description,
        isLegacy: formData.isLegacy,
        h2oIdentifier: formData.h2oIdentifier,
        config: JSON.stringify(configObj)
      };

      // Conditionally add flags based on feature type
      if (isSyntheticScore()) {
        // For synthetic features, include isImplicit
        payload.isImplicit = formData.isImplicit;
      } else {
        // For non-synthetic features, include isDefault
        payload.isDefault = formData.isDefault;
      }

      const response = await apiCallSmart('/api/2/fraudmodel/register', {
        method: 'POST',
        data: payload
      });

      if (response.data.status === 'ok') {
        toaster.success(response.data.msg || 'Model created successfully');
        handleClose();
        onSuccess();
      } else {
        toaster.error(response.data.msg || 'Failed to create model');
      }
    } catch (error) {
      console.error('Error creating model:', error);
      toaster.error('Failed to create model');
    } finally {
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      commonName: '',
      description: '',
      productId: '',
      defaultResponseName: '',
      defaultResponseVersion: '',
      feature: '',
      engine: ENGINE,
      h2oIdentifier: '',
      isDefault: false,
      isLegacy: false,
      isImplicit: false,
      lowerThreshold: '',
      upperThreshold: '',
      positiveThreshold: '',
      negativeThreshold: '',
      mutedParams: ''
    });
    setErrors({});
    setSubmitting(false);
    setAvailableIdentifiers([]);
    setSelectedFeature(null);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Typography variant="h6">Create New Model</Typography>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 2 }}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Model Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Common Name"
                value={formData.commonName}
                onChange={(e) => handleInputChange('commonName', e.target.value)}
                error={!!errors.commonName}
                helperText={errors.commonName}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                error={!!errors.description}
                helperText={errors.description}
                multiline
                rows={3}
                required
              />
            </Grid>

            {/* Product and Response Configuration */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                Product and Response Configuration
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={!!errors.productId} required>
                <InputLabel>Product</InputLabel>
                <Select
                  value={formData.productId}
                  label="Product"
                  onChange={(e) => handleInputChange('productId', e.target.value)}
                >
                  {PRODUCTS.map((product) => (
                    <MenuItem key={product.id} value={product.id}>
                      {product.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.productId && <FormHelperText>{errors.productId}</FormHelperText>}
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Engine"
                value="H2O"
                InputProps={{
                  readOnly: true,
                }}
                helperText="All models use H2O engine"
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Default Response Name"
                value={formData.defaultResponseName}
                onChange={(e) => handleInputChange('defaultResponseName', e.target.value)}
                error={!!errors.defaultResponseName}
                helperText={errors.defaultResponseName}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Default Response Version"
                value={formData.defaultResponseVersion}
                onChange={(e) => handleInputChange('defaultResponseVersion', e.target.value)}
                error={!!errors.defaultResponseVersion}
                helperText={errors.defaultResponseVersion || 'Enter version number (e.g., 1.0, 2.5)'}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <Autocomplete
                options={availableIdentifiers}
                value={formData.h2oIdentifier}
                onChange={(event, newValue) => handleInputChange('h2oIdentifier', newValue || '')}
                loading={loadingIdentifiers}
                disabled={loadingIdentifiers}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="H2O Identifier"
                    error={!!errors.h2oIdentifier}
                    helperText={errors.h2oIdentifier || 'Select from available H2O MOJO model identifiers'}
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth error={!!errors.feature} required>
                <InputLabel>Feature</InputLabel>
                <Select
                  value={formData.feature}
                  label="Feature"
                  onChange={(e) => handleFeatureChange(e.target.value as string)}
                >
                  {FEATURES.map((feature) => (
                    <MenuItem key={feature.id} value={feature.id}>
                      {feature.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.feature && <FormHelperText>{errors.feature}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Dynamic Thresholds based on Feature Type */}
            {selectedFeature && (
              <>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                    Threshold Configuration ({selectedFeature.name})
                  </Typography>
                </Grid>

                {/* Risk Score Thresholds */}
                {isRiskScore() && (
                  <>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Lower Threshold"
                        value={formData.lowerThreshold}
                        onChange={(e) => handleInputChange('lowerThreshold', e.target.value)}
                        error={!!errors.lowerThreshold}
                        helperText={errors.lowerThreshold || 'Lower threshold for risk score'}
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Upper Threshold"
                        value={formData.upperThreshold}
                        onChange={(e) => handleInputChange('upperThreshold', e.target.value)}
                        error={!!errors.upperThreshold}
                        helperText={errors.upperThreshold || 'Upper threshold (0.0 - 1.0)'}
                        type="number"
                        inputProps={{ min: 0, max: 1, step: 0.1 }}
                      />
                    </Grid>
                  </>
                )}

                {/* Correlation and Synthetic Score Thresholds */}
                {(isCorrelationScore() || isSyntheticScore()) && (
                  <>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Positive Threshold"
                        value={formData.positiveThreshold}
                        onChange={(e) => handleInputChange('positiveThreshold', e.target.value)}
                        error={!!errors.positiveThreshold}
                        helperText={errors.positiveThreshold || `Positive threshold for ${selectedFeature.name.toLowerCase()} score`}
                        type="number"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Negative Threshold"
                        value={formData.negativeThreshold}
                        onChange={(e) => handleInputChange('negativeThreshold', e.target.value)}
                        error={!!errors.negativeThreshold}
                        helperText={errors.negativeThreshold || 'Negative threshold (0.0 - 1.0)'}
                        type="number"
                        inputProps={{ min: 0, max: 1, step: 0.1 }}
                      />
                    </Grid>
                  </>
                )}

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Muted Parameters"
                    value={formData.mutedParams}
                    onChange={(e) => handleInputChange('mutedParams', e.target.value)}
                    helperText="Comma separated rulecodes in the format FMVAL.xxxx,FMVAL.yyyy,..."
                    placeholder="FMVAL.xxxx,FMVAL.yyyy"
                  />
                </Grid>
              </>
            )}

            {/* Model Settings */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                Model Settings
              </Typography>
            </Grid>

            {/* Conditionally show isDefault or isImplicit based on feature type */}
            {selectedFeature && (
              <Grid item xs={12} md={6}>
                {isSyntheticScore() ? (
                  <FormControl fullWidth>
                    <InputLabel>Implicit</InputLabel>
                    <Select
                      value={formData.isImplicit.toString()}
                      label="Implicit"
                      onChange={(e) => handleInputChange('isImplicit', e.target.value === 'true')}
                    >
                      <MenuItem value="false">False</MenuItem>
                      <MenuItem value="true">True</MenuItem>
                    </Select>
                    <FormHelperText>Set implicit flag for synthetic models</FormHelperText>
                  </FormControl>
                ) : (
                  <FormControl fullWidth>
                    <InputLabel>Default Model</InputLabel>
                    <Select
                      value={formData.isDefault.toString()}
                      label="Default Model"
                      onChange={(e) => handleInputChange('isDefault', e.target.value === 'true')}
                    >
                      <MenuItem value="false">False</MenuItem>
                      <MenuItem value="true">True</MenuItem>
                    </Select>
                    <FormHelperText>Set this model as the default for its feature</FormHelperText>
                  </FormControl>
                )}
              </Grid>
            )}

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Legacy Model</InputLabel>
                <Select
                  value={formData.isLegacy.toString()}
                  label="Legacy Model"
                  onChange={(e) => handleInputChange('isLegacy', e.target.value === 'true')}
                >
                  <MenuItem value="false">False</MenuItem>
                  <MenuItem value="true">True</MenuItem>
                </Select>
                <FormHelperText>Mark this model as legacy</FormHelperText>
              </FormControl>
            </Grid>


          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={submitting}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={submitting}
        >
          {submitting ? 'Creating...' : 'Create Model'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
