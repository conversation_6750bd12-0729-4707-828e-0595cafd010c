import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Grid,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  InputAdornment,
  TablePagination,
  Chip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { Model } from '../model';
import { CreateModelDialog } from '../CreateModel';

import { toaster } from '../../../Components/Toaster';
import { apiCallV2 } from '../../../Utilities';

// Mock data for products
const PRODUCTS = [
  { id: 1, name: '<PERSON>' },
  { id: 2, name: '<PERSON><PERSON><PERSON>' },
  { id: 3, name: '<PERSON><PERSON>' },
  { id: 4, name: 'Email Risk' }
];

const ModelListing: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<number | ''>(1);
  const [showActiveOnly, setShowActiveOnly] = useState<boolean>(true);
  const [showDefaultOnly, setShowDefaultOnly] = useState<boolean>(false);
  const [models, setModels] = useState<Model[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [modelToMarkLegacy, setModelToMarkLegacy] = useState<Model | null>(null);
  const [showActivationDialog, setShowActivationDialog] = useState(false);
  const [modelToToggle, setModelToToggle] = useState<Model | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);



  // Function to fetch models data
const fetchModels = useCallback(async () => {
  setLoading(true);
  try {
    const params: any = {};
    if (selectedProduct) params.productId = selectedProduct;
    if (showActiveOnly) params.isActive = true;

    const response = await apiCallV2<{ status: string; msg: string; data: Model[] }>(
      '/fraudmodel/models',
      {
        method: 'GET',
        params
      }
    );

    const modelArray = Array.isArray(response.data?.data) ? response.data.data : [];

    let filteredModels = modelArray;

    if (showDefaultOnly) {
      filteredModels = filteredModels.filter((model) => model.default);
    }
    if(showActiveOnly){
        filteredModels = filteredModels.filter((model) => model.active);
    }

    console.log("models:", modelArray);
    console.log("filteredModels:", filteredModels);

    setModels(filteredModels);
    toaster.success('Models loaded successfully');
  } catch (error) {
    console.error('Error fetching models:', error);
    toaster.error('Failed to load models');
  } finally {
    setLoading(false);
  }
}, [selectedProduct, showActiveOnly, showDefaultOnly]);


useEffect(() => {
  fetchModels();
}, [fetchModels]);

const toggleModelActivation = async (model: Model) => {
  const action = model.active ? 'deactivate' : 'activate';
  try {
    const response = await apiCallV2<{ status: string; msg: string }>(
      `/fraudmodel/${action}`,
      {
        method: 'POST',
        params: { modelId: model.modelId }
      }
    );

    if (response.data.status === 'Ok') {
      toaster.success(`${model.name} ${action}d successfully`);
      fetchModels(); // Refresh list
    } else {
      toaster.error(`Failed to ${action} model: ${response.data.msg}`);
    }
  } catch (error) {
    console.error(`Error calling ${action}:`, error);
    toaster.error(`Error calling ${action} model`);
  }
};

const markModelAsLegacy = async (model: Model) => {
  try {
    const response = await apiCallV2<{ status: string; msg: string; data?: string }>(
      '/fraudmodel/mark_legacy',
      {
        method: 'POST',
        params: { modelId: model.modelId }
      }
    );

    if (response.data.status === 'Ok') {
      toaster.success(`${model.name} marked as legacy.`);
      fetchModels(); // Refresh model list
    } else {
      toaster.error(`Failed to mark legacy: ${response.data.msg}`);
    }
  } catch (error) {
    console.error('Error marking legacy:', error);
    toaster.error('Error marking model as legacy');
  }
};



  // Filter controls component
  const renderFilterControls = useMemo(() => {
    return (
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <FormControl fullWidth size="small">
                <InputLabel id="product-select-label">Product</InputLabel>
                <Select
                  labelId="product-select-label"
                  id="product-select"
                  value={selectedProduct}
                  label="Product"
                  onChange={(e) => setSelectedProduct(e.target.value as number | '')}
                >
                  <MenuItem value="">
                    <em>All Products</em>
                  </MenuItem>
                  {PRODUCTS.map((product) => (
                    <MenuItem key={product.id} value={product.id}>
                      {product.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl component="fieldset">
                <Typography variant="subtitle2" gutterBottom>
                  Model Status
                </Typography>
                <RadioGroup
                  row
                  value={showActiveOnly.toString()}
                  onChange={(e) => setShowActiveOnly(e.target.value === 'true')}
                >
                  <FormControlLabel
                    value="true"
                    control={<Radio />}
                    label="Show Active Models Only"
                  />
                  <FormControlLabel
                    value="false"
                    control={<Radio />}
                    label="Show All Models"
                  />
                </RadioGroup>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl component="fieldset">
                <Typography variant="subtitle2" gutterBottom>
                  Default Status
                </Typography>
                <RadioGroup
                  row
                  value={showDefaultOnly.toString()}
                  onChange={(e) => setShowDefaultOnly(e.target.value === 'true')}
                >
                  <FormControlLabel
                    value="true"
                    control={<Radio />}
                    label="Show Default Models Only"
                  />
                  <FormControlLabel
                    value="false"
                    control={<Radio />}
                    label="Show All Models"
                  />
                </RadioGroup>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  }, [selectedProduct, showActiveOnly, showDefaultOnly]);

  // Models table component
  const renderModelsTable = useMemo(() => {
    return (
      <Card variant="outlined">
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Models</Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setShowCreateDialog(true)}
                color="primary"
              >
                Add Model
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchModels}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          </Box>
          <TableContainer sx={{ maxWidth: '100%', overflowX: 'auto' }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Model ID</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Common Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Default Response</TableCell>
                  <TableCell>Response Version</TableCell>
                  <TableCell>Product</TableCell>
                  <TableCell>Engine</TableCell>
                  <TableCell>Legacy</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Default</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={12} align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                        <CircularProgress size={24} />
                        <Typography sx={{ ml: 2 }}>Loading models...</Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : models.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={12} align="center">
                      <Typography>No models found matching the criteria</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  models.map((model) => (
                    <TableRow key={model.modelId}>
                      <TableCell>{model.modelId}</TableCell>
                      <TableCell>{model.name}</TableCell>
                      <TableCell>{model.commonName}</TableCell>
                      <TableCell>{model.description}</TableCell>
                      <TableCell>{model.defaultResponseName}</TableCell>
                      <TableCell>{model.defaultResponseVersion}</TableCell>
                      <TableCell>
                        {PRODUCTS.find(p => p.id === model.productId)?.name || `Product ${model.productId}`}
                      </TableCell>
                      <TableCell>{model.engine}</TableCell>
                      <TableCell>{model.legacy ? 'Yes' : 'No'}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {model.active ?
                            <CheckCircleIcon fontSize="small" color="success" sx={{ mr: 0.5 }} /> :
                            <CancelIcon fontSize="small" color="error" sx={{ mr: 0.5 }} />
                          }
                          <span>{model.active ? 'Active' : 'Inactive'}</span>
                        </Box>
                      </TableCell>
                      <TableCell>
                        {model.default ? 'Yes' : 'No'}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{ mr: 1 }}
                          onClick={() => toaster.info(`Viewing details for ${model.name}`)}
                        >
                          View
                        </Button>
                       <Button
                         variant="outlined"
                         size="small"
                         color={model.active ? 'error' : 'success'}
                         sx={{ mr: 1 }}
                        onClick={() => {
                          setModelToToggle(model);
                          setShowActivationDialog(true);
                        }}
                       >
                         {model.active ? 'Deactivate' : 'Activate'}
                       </Button>
                        {!model.legacy && (
                          <Button
                            variant="outlined"
                            size="small"
                            color="warning"
                            onClick={() => {
                              setModelToMarkLegacy(model);
                              setShowConfirmDialog(true);
                            }}
                          >
                            Mark as Legacy
                          </Button>
                        )}

                        {!model.default && (
                          <Button
                            variant="outlined"
                            size="small"
                            color="primary"
                            onClick={() => {
                              // In a real application, you would call an API to set this model as default
                              toaster.success(`Set ${model.name} as default for ${PRODUCTS.find(p => p.id === model.productId)?.name || `Product ${model.productId}`}`);
                              // Update the local state for demonstration
                              const updatedModels = models.map(m =>
                                m.productId === model.productId ?
                                  {...m, default: m.modelId === model.modelId} : m
                              );
                              setModels(updatedModels);
                            }}
                          >
                            Set as Default
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  }, [models, loading, fetchModels]);

  return (
    <Grid container spacing={2} direction="column">
    <Dialog open={showActivationDialog} onClose={() => setShowActivationDialog(false)}>
      <DialogTitle>
        Confirm {modelToToggle?.active ? 'Deactivation' : 'Activation'}
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          Are you sure you want to {modelToToggle?.active ? 'deactivate' : 'activate'}{' '}
          <strong>{modelToToggle?.name}</strong>?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowActivationDialog(false)} color="inherit">
          Cancel
        </Button>
        <Button
          color={modelToToggle?.active ? 'error' : 'success'}
          variant="contained"
          onClick={async () => {
            if (modelToToggle) {
              await toggleModelActivation(modelToToggle);
              setShowActivationDialog(false);
              setModelToToggle(null);
            }
          }}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>

     <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)}>
            <DialogTitle>Confirm Mark as Legacy</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Are you sure you want to mark <strong>{modelToMarkLegacy?.name}</strong> as legacy?
                This action cannot be undone.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setShowConfirmDialog(false)} color="inherit">
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  if (modelToMarkLegacy) {
                    await markModelAsLegacy(modelToMarkLegacy);
                    setShowConfirmDialog(false);
                    setModelToMarkLegacy(null);
                  }
                }}
                color="warning"
                variant="contained"
              >
                Confirm
              </Button>
            </DialogActions>
          </Dialog>
      <Grid item>
        <Typography variant="h5" align="center" gutterBottom>
          Fraud Model Manager
        </Typography>
      </Grid>

      <Grid item>
        {renderFilterControls}
      </Grid>

      <Grid item>
        {renderModelsTable}
      </Grid>
      <CreateModelDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onSuccess={() => {
          setShowCreateDialog(false);
          fetchModels();
        }}
      />
    </Grid>
  );
};

export default ModelListing;
