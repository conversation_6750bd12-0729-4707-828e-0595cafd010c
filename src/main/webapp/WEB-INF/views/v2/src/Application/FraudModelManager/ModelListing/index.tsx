import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Grid,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  InputAdornment,
  TablePagination,
  Chip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { Model } from '../model';
import { CreateModelDialog } from '../CreateModel';

import { toaster } from '../../../Components/Toaster';
import { apiCallV2 } from '../../../Utilities';

// Mock data for products
const PRODUCTS = [
  { id: 1, name: 'Sigma Identity' },
  { id: 2, name: 'Sigma Synthetic' },
  { id: 3, name: 'Address Risk' },
  { id: 4, name: 'Email Risk' },
  { id: 5, name: 'Phone Risk' }
];

// Feature mapping
const FEATURES = [
  { id: 'fraud', name: 'Fraud' },
  { id: 'synthetic', name: 'Synthetic' },
  { id: 'correlation', name: 'Correlation' },
  { id: 'risk', name: 'Risk Score' }
];

const ModelListing: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<number | ''>(1);
  const [showActiveOnly, setShowActiveOnly] = useState<boolean>(true);
  const [showDefaultOnly, setShowDefaultOnly] = useState<boolean>(false);
  const [models, setModels] = useState<Model[]>([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [modelToMarkLegacy, setModelToMarkLegacy] = useState<Model | null>(null);
  const [showActivationDialog, setShowActivationDialog] = useState(false);
  const [modelToToggle, setModelToToggle] = useState<Model | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Search and pagination state
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);



  // Function to fetch models data
const fetchModels = useCallback(async () => {
  setLoading(true);
  try {
    const params: any = {};
    if (selectedProduct) params.productId = selectedProduct;
    if (showActiveOnly) params.isActive = true;

    const response = await apiCallV2<{ status: string; msg: string; data: Model[] }>(
      '/fraudmodel/models',
      {
        method: 'GET',
        params
      }
    );

    const modelArray = Array.isArray(response.data?.data) ? response.data.data : [];

    let filteredModels = modelArray;

    if (showDefaultOnly) {
      filteredModels = filteredModels.filter((model) => model.default);
    }
    if(showActiveOnly){
        filteredModels = filteredModels.filter((model) => model.active);
    }

    console.log("models:", modelArray);
    console.log("filteredModels:", filteredModels);

    setModels(filteredModels);
    toaster.success('Models loaded successfully');
  } catch (error) {
    console.error('Error fetching models:', error);
    toaster.error('Failed to load models');
  } finally {
    setLoading(false);
  }
}, [selectedProduct, showActiveOnly, showDefaultOnly]);


useEffect(() => {
  fetchModels();
}, [fetchModels]);

// Helper function to get feature name
const getFeatureName = (featureId: number) => {
  // Assuming featureId maps to FEATURES array index + 1
  const feature = FEATURES[featureId - 1];
  return feature ? feature.name : `Feature ${featureId}`;
};

// Filtered and paginated models
const filteredModels = useMemo(() => {
  let filtered = models;

  // Apply search filter
  if (searchTerm) {
    filtered = filtered.filter(model =>
      model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.commonName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.modelId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.defaultResponseName.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  return filtered;
}, [models, searchTerm]);

// Paginated models
const paginatedModels = useMemo(() => {
  const startIndex = page * rowsPerPage;
  return filteredModels.slice(startIndex, startIndex + rowsPerPage);
}, [filteredModels, page, rowsPerPage]);

// Handle pagination
const handleChangePage = (event: unknown, newPage: number) => {
  setPage(newPage);
};

const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
  setRowsPerPage(parseInt(event.target.value, 10));
  setPage(0);
};

const toggleModelActivation = async (model: Model) => {
  const action = model.active ? 'deactivate' : 'activate';
  try {
    const response = await apiCallV2<{ status: string; msg: string }>(
      `/fraudmodel/${action}`,
      {
        method: 'POST',
        params: { modelId: model.modelId }
      }
    );

    if (response.data.status === 'Ok') {
      toaster.success(`${model.name} ${action}d successfully`);
      fetchModels(); // Refresh list
    } else {
      toaster.error(`Failed to ${action} model: ${response.data.msg}`);
    }
  } catch (error) {
    console.error(`Error calling ${action}:`, error);
    toaster.error(`Error calling ${action} model`);
  }
};

const markModelAsLegacy = async (model: Model) => {
  try {
    const response = await apiCallV2<{ status: string; msg: string; data?: string }>(
      '/fraudmodel/mark_legacy',
      {
        method: 'POST',
        params: { modelId: model.modelId }
      }
    );

    if (response.data.status === 'Ok') {
      toaster.success(`${model.name} marked as legacy.`);
      fetchModels(); // Refresh model list
    } else {
      toaster.error(`Failed to mark legacy: ${response.data.msg}`);
    }
  } catch (error) {
    console.error('Error marking legacy:', error);
    toaster.error('Error marking model as legacy');
  }
};



  // Filter controls component
  const renderFilterControls = useMemo(() => {
    return (
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            {/* Search Box */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search models by name, ID, or response name..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setPage(0); // Reset to first page when searching
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel id="product-select-label">Product</InputLabel>
                <Select
                  labelId="product-select-label"
                  id="product-select"
                  value={selectedProduct}
                  label="Product"
                  onChange={(e) => setSelectedProduct(e.target.value as number | '')}
                >
                  <MenuItem value="">
                    <em>All Products</em>
                  </MenuItem>
                  {PRODUCTS.map((product) => (
                    <MenuItem key={product.id} value={product.id}>
                      {product.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  label="Active Only"
                  color={showActiveOnly ? 'primary' : 'default'}
                  onClick={() => setShowActiveOnly(!showActiveOnly)}
                  variant={showActiveOnly ? 'filled' : 'outlined'}
                  size="small"
                />
                <Chip
                  label="Default Only"
                  color={showDefaultOnly ? 'secondary' : 'default'}
                  onClick={() => setShowDefaultOnly(!showDefaultOnly)}
                  variant={showDefaultOnly ? 'filled' : 'outlined'}
                  size="small"
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  }, [selectedProduct, showActiveOnly, showDefaultOnly, searchTerm]);

  // Models table component
  const renderModelsTable = useMemo(() => {
    return (
      <Card variant="outlined">
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Box>
              <Typography variant="h6">Models</Typography>
              <Typography variant="body2" color="text.secondary">
                {searchTerm ? (
                  `Showing ${filteredModels.length} of ${models.length} models matching "${searchTerm}"`
                ) : (
                  `Total: ${models.length} models`
                )}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setShowCreateDialog(true)}
                color="primary"
              >
                Add Model
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchModels}
                disabled={loading}
              >
                Refresh
              </Button>
            </Box>
          </Box>
          <TableContainer sx={{ maxWidth: '100%', overflowX: 'auto' }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Model ID</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Common Name</TableCell>
                  <TableCell>Feature</TableCell>
                  <TableCell>Default Response</TableCell>
                  <TableCell>Response Version</TableCell>
                  <TableCell>Legacy</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Default</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={10} align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                        <CircularProgress size={24} />
                        <Typography sx={{ ml: 2 }}>Loading models...</Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : filteredModels.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} align="center">
                      <Typography>
                        {searchTerm ? `No models found matching "${searchTerm}"` : 'No models found matching the criteria'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedModels.map((model) => (
                    <TableRow key={model.modelId}>
                      <TableCell>{model.modelId}</TableCell>
                      <TableCell>{model.name}</TableCell>
                      <TableCell>{model.commonName}</TableCell>
                      <TableCell>
                        <Chip
                          label={getFeatureName(model.featureId)}
                          size="small"
                          variant="outlined"
                          color="primary"
                        />
                      </TableCell>
                      <TableCell>{model.defaultResponseName}</TableCell>
                      <TableCell>{model.defaultResponseVersion}</TableCell>
                      <TableCell>
                        <Chip
                          label={model.legacy ? 'Yes' : 'No'}
                          size="small"
                          color={model.legacy ? 'warning' : 'default'}
                          variant={model.legacy ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {model.active ?
                            <CheckCircleIcon fontSize="small" color="success" sx={{ mr: 0.5 }} /> :
                            <CancelIcon fontSize="small" color="error" sx={{ mr: 0.5 }} />
                          }
                          <span>{model.active ? 'Active' : 'Inactive'}</span>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={model.default ? 'Yes' : 'No'}
                          size="small"
                          color={model.default ? 'success' : 'default'}
                          variant={model.default ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{ mr: 1 }}
                          onClick={() => toaster.info(`Viewing details for ${model.name}`)}
                        >
                          View
                        </Button>
                       <Button
                         variant="outlined"
                         size="small"
                         color={model.active ? 'error' : 'success'}
                         sx={{ mr: 1 }}
                        onClick={() => {
                          setModelToToggle(model);
                          setShowActivationDialog(true);
                        }}
                       >
                         {model.active ? 'Deactivate' : 'Activate'}
                       </Button>
                        {!model.legacy && (
                          <Button
                            variant="outlined"
                            size="small"
                            color="warning"
                            onClick={() => {
                              setModelToMarkLegacy(model);
                              setShowConfirmDialog(true);
                            }}
                          >
                            Mark as Legacy
                          </Button>
                        )}

                        {!model.default && (
                          <Button
                            variant="outlined"
                            size="small"
                            color="primary"
                            onClick={() => {
                              // In a real application, you would call an API to set this model as default
                              const productName = PRODUCTS.find(p => p.id === model.productId)?.name || `Product ${model.productId}`;
                              toaster.success(`Set ${model.name} as default for ${productName}`);
                              // Update the local state for demonstration
                              const updatedModels = models.map(m =>
                                m.productId === model.productId ?
                                  {...m, default: m.modelId === model.modelId} : m
                              );
                              setModels(updatedModels);
                            }}
                          >
                            Set as Default
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredModels.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            sx={{ borderTop: 1, borderColor: 'divider' }}
          />
        </CardContent>
      </Card>
    );
  }, [paginatedModels, filteredModels.length, page, rowsPerPage, loading, fetchModels, handleChangePage, handleChangeRowsPerPage]);

  return (
    <Grid container spacing={2} direction="column">
    <Dialog open={showActivationDialog} onClose={() => setShowActivationDialog(false)}>
      <DialogTitle>
        Confirm {modelToToggle?.active ? 'Deactivation' : 'Activation'}
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          Are you sure you want to {modelToToggle?.active ? 'deactivate' : 'activate'}{' '}
          <strong>{modelToToggle?.name}</strong>?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowActivationDialog(false)} color="inherit">
          Cancel
        </Button>
        <Button
          color={modelToToggle?.active ? 'error' : 'success'}
          variant="contained"
          onClick={async () => {
            if (modelToToggle) {
              await toggleModelActivation(modelToToggle);
              setShowActivationDialog(false);
              setModelToToggle(null);
            }
          }}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>

     <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)}>
            <DialogTitle>Confirm Mark as Legacy</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Are you sure you want to mark <strong>{modelToMarkLegacy?.name}</strong> as legacy?
                This action cannot be undone.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setShowConfirmDialog(false)} color="inherit">
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  if (modelToMarkLegacy) {
                    await markModelAsLegacy(modelToMarkLegacy);
                    setShowConfirmDialog(false);
                    setModelToMarkLegacy(null);
                  }
                }}
                color="warning"
                variant="contained"
              >
                Confirm
              </Button>
            </DialogActions>
          </Dialog>
      <Grid item>
        <Typography variant="h4" align="center" gutterBottom sx={{
          fontWeight: 600,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          mb: 3
        }}>
          Fraud Model Manager Reimagined
        </Typography>
      </Grid>

      <Grid item>
        {renderFilterControls}
      </Grid>

      <Grid item>
        {renderModelsTable}
      </Grid>
      <CreateModelDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onSuccess={() => {
          setShowCreateDialog(false);
          fetchModels();
        }}
      />
    </Grid>
  );
};

export default ModelListing;
